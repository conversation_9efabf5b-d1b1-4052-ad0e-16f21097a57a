import https from '../../../utils/http';
import authService from '../../../utils/auth';
import toastService from '../../../utils/toast.service';
import dateHelper from '../../../utils/date-helper';
import GlobalConfig from '../../../utils/global-config';
const app = getApp();
Page({
  data: {
    chatId: '',
    orderId: '',
    orderNo: '',
    scrollTop: 0,
    inputMessage: '',
    messageList: [],
    customerAvatar: '/static/images/avatar1.jpg',
    merchantAvatar: '/static/images/avatar2.jpg',
    // 定时器ID
    chatDetailTimer: null,
    memberId: null
  },
  
  onLoad(options) {
    if (options.orderId) {
      this.setData({
        orderId: options.orderId,
        orderNo: options.orderNo,
        memberId: app.globalData.loginUserInfo.id
      });
      
      // 设置顶部标题
      wx.setNavigationBarTitle({
        title: '在线客服'
      });
      
      // 加载聊天记录
      this.loadChatDetail();
      
      // 启动定时器，每5秒刷新一次聊天详情
      this.startChatDetailTimer();
    }
  },
  
  onUnload() {
    // 页面销毁时清除定时器
    this.clearChatDetailTimer();
  },
  
  // 加载聊天详情
  loadChatDetail() {
    const memberId = this.data.memberId;

    https.request('/api-order/rest/member/order/selectChatDetailByOrderId/' + this.data.orderId).then(res => {
      if (res.code === 200) {
        const data = res.data || [];
        // debugger
        // 格式化消息列表
        const messages = data.onlineChatDetailResults || [];
        const formattedMessages = messages.map(item => ({
          sender: item.sendId === memberId ? 'me' : 'other', // me-用户发送 other-商家发送
          content: item.message_info || '',
          time: this.formatTime(item.createTime),
          avatar: item.sendId === memberId ? data.onlineChatResult?.headImg : GlobalConfig.ossUrl + data.onlineChatResult?.shopLogoImg
        }));
        // debugger
        this.setData({
          messageList: formattedMessages,
          chatId: data.onlineChatResult?.id
        });

        // 页面加载完成后滚动到底部
        this.scrollToBottom();
      } else {
        toastService.showToast(res.message || '获取聊天详情失败');
      }
    }).catch(err => {
      // toastService.showToast('网络请求失败');
      console.log(err)
    });
  },

  // 格式化时间
  formatTime(timestamp) {
    if (!timestamp) return '';

    const date = new Date(timestamp);
    const hours = date.getHours().toString().padStart(2, '0');
    const minutes = date.getMinutes().toString().padStart(2, '0');
    return `${hours}:${minutes}`;
  },

  // 发送消息
  sendMessage() {
    if (!this.data.inputMessage.trim()) {
      toastService.showToast('请输入消息内容');
      return;
    }

   const memberId = this.data.memberId;

    // 先在界面上显示消息
    const newMessage = {
      sender: 'me',
      content: this.data.inputMessage,
      time: this.getCurrentTime(),
      avatar: this.data.customerAvatar
    };

    const messageList = this.data.messageList.concat(newMessage);
    const message = this.data.inputMessage;
    
    this.setData({
      messageList: messageList,
      inputMessage: ''
    });

    // 发送消息到服务器
    https.request('/api-user/rest/member/onlineChat/sendMessage', {
      id: this.data.chatId,
      memberId: memberId,
      messageType: 0, // 0-文本
      messageInfo: message
    }).then(res => {
      if (res.code !== 200) {
        toastService.showToast(res.message || '发送失败');
      }
    }).catch(err => {
      toastService.showToast('网络请求失败');
    });

    // 发送消息后滚动到底部
    this.scrollToBottom();
  },

  // 滚动到底部
  scrollToBottom() {
    const query = wx.createSelectorQuery();
    query.select('.message-list').boundingClientRect();
    query.selectViewport().scrollOffset();
    query.exec(res => {
      if (res && res[0] && res[1]) {
        wx.pageScrollTo({
          scrollTop: res[0].height,
          duration: 300
        });
      }
    });
  },

  // 获取当前时间（格式：HH:mm）
  getCurrentTime() {
    const now = new Date();
    const hours = String(now.getHours()).padStart(2, '0');
    const minutes = String(now.getMinutes()).padStart(2, '0');
    return `${hours}:${minutes}`;
  },
  
  // 输入框输入事件
  onInputChange(e) {
    this.setData({
      inputMessage: e.detail.value
    });
  },
  
  // 启动定时器，每5秒刷新一次聊天详情
  startChatDetailTimer() {
    // 先清除可能存在的定时器
    this.clearChatDetailTimer();
    
    // 设置新的定时器
    this.chatDetailTimer = setInterval(() => {
      this.loadChatDetail()
    }, 5000); // 5秒刷新一次
  },
  
  // 清除定时器
  clearChatDetailTimer() {
    if (this.chatDetailTimer) {
      clearInterval(this.chatDetailTimer);
      this.chatDetailTimer = null;
    }
  }
})