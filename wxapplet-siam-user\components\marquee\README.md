# 跑马灯组件 (Marquee Component)

## 功能描述
跑马灯组件用于在页面上滚动显示公告信息。组件会自动从API获取公告数据，并将所有公告内容拼接后进行滚动展示。

## API接口
- **接口地址**: `/api-merchant/rest/admin/announcements/selectByExample`
- **请求方法**: POST
- **请求参数**: 
  ```json
  {
    "pageNo": 1,
    "pageSize": 10
  }
  ```

## 使用方法

### 1. 在页面JSON文件中注册组件
```json
{
  "usingComponents": {
    "marquee": "../../components/marquee/marquee"
  }
}
```

### 2. 在WXML中使用组件

#### 基础用法（自动获取API数据）
```xml
<marquee></marquee>
```

#### 手动传入数据
```xml
<marquee announcements="{{announcements}}" autoFetch="{{false}}"></marquee>
```

#### 自定义滚动速度
```xml
<marquee speed="{{30}}"></marquee>
```

## 组件属性

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| announcements | Array | [] | 公告数据数组，每个元素需包含content字段 |
| speed | Number | 50 | 滚动速度，数值越小滚动越快 |
| autoFetch | Boolean | true | 是否自动从API获取数据 |

## 数据格式
公告数据应为数组格式，每个元素包含content字段：
```javascript
[
  { content: "第一条公告内容" },
  { content: "第二条公告内容" },
  { content: "第三条公告内容" }
]
```

## 样式说明
- 组件使用橙色主题，背景为浅橙色
- 左侧显示活动图标
- 文本内容会自动滚动，如果内容宽度小于容器宽度则不滚动
- 支持响应式布局

## 注意事项
1. 如果API请求失败，组件会自动使用测试数据进行展示
2. 组件会自动处理空数据的情况
3. 滚动动画使用微信小程序原生动画API实现
4. 组件在页面销毁时会自动清理定时器，避免内存泄漏

## 测试页面
项目中包含测试页面 `pages/test-marquee/test-marquee`，可用于验证组件功能。
