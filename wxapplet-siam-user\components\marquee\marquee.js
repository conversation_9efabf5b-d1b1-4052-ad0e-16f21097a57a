import https from '../../utils/http';

Component({
  properties: {
    // 可以通过外部传入公告数据，如果不传则自动获取
    announcements: {
      type: Array,
      value: []
    },
    // 滚动速度，数值越小滚动越快
    speed: {
      type: Number,
      value: 25
    },
    // 是否自动获取数据
    autoFetch: {
      type: Boolean,
      value: true
    }
  },

  data: {
    marqueeText: '',
    animationData: null,
    containerWidth: 0,
    textWidth: 0,
    timer: null
  },

  lifetimes: {
    attached() {
      if (this.data.autoFetch) {
        this.fetchAnnouncements();
      } else if (this.data.announcements.length > 0) {
        this.initMarquee();
      }
    },
    detached() {
      this.clearTimer();
    }
  },

  observers: {
    'announcements': function(newVal) {
      if (newVal && newVal.length > 0) {
        this.initMarquee();
      }
    }
  },

  methods: {
    // 获取公告数据
    fetchAnnouncements() {
      https.request('/api-merchant/rest/member/announcements/selectAllPublished',{}, {}).then(result => {
        if (result.success && result.data) {
          this.setData({
            announcements: result.data
          });
          this.initMarquee();
        } else {
          // 如果API返回成功但没有数据，使用测试数据
          // this.setTestData();
        }
      }).catch(err => {
        console.error('获取公告数据失败:', err);
        // API请求失败时使用测试数据
        // this.setTestData();
      });
    },

    // 设置测试数据（用于开发测试）
    setTestData() {
      const testData = [
        { content: '欢迎使用湖工速达！' },
        { content: '新用户注册即可获得优惠券' },
        { content: '今日特价商品限时抢购中' },
        { content: '配送服务覆盖全校区' }
      ];
      this.setData({
        announcements: testData
      });
      this.initMarquee();
    },

    // 初始化跑马灯
    initMarquee() {
      const announcements = this.data.announcements;
      if (!announcements || announcements.length === 0) {
        return;
      }

      // 将所有公告内容拼接，用空格分隔
      const marqueeText = announcements
        .map(item => item.content || '')
        .filter(content => content.trim())
        .join('   ');

      this.setData({
        marqueeText: marqueeText
      });

      // 获取容器和文本宽度后开始动画
      this.getElementsSize();
    },

    // 获取元素尺寸
    getElementsSize() {
      const query = this.createSelectorQuery();
      
      // 获取容器宽度
      query.select('.marquee-content').boundingClientRect((rect) => {
        if (rect) {
          this.setData({
            containerWidth: rect.width
          });
        }
      });

      // 获取文本宽度
      query.select('.marquee-text').boundingClientRect((rect) => {
        if (rect) {
          this.setData({
            textWidth: rect.width
          });
          // 开始滚动动画
          this.startMarquee();
        }
      });

      query.exec();
    },

    // 开始跑马灯动画
    startMarquee() {
      const { containerWidth, textWidth, speed } = this.data;
      
      if (textWidth <= containerWidth) {
        // 文本宽度小于容器宽度，不需要滚动
        return;
      }

      this.clearTimer();

      const totalDistance = textWidth + containerWidth;
      const duration = totalDistance * speed;

      // 创建动画
      const animation = wx.createAnimation({
        duration: duration,
        timingFunction: 'linear'
      });

      // 设置初始位置
      animation.translateX(containerWidth).step({ duration: 0 });
      this.setData({
        animationData: animation.export()
      });

      // 开始滚动
      setTimeout(() => {
        animation.translateX(-textWidth).step({ duration: duration });
        this.setData({
          animationData: animation.export()
        });

        // 设置循环定时器
        this.data.timer = setTimeout(() => {
          this.startMarquee();
        }, duration);
      }, 100);
    },

    // 清除定时器
    clearTimer() {
      if (this.data.timer) {
        clearTimeout(this.data.timer);
        this.setData({
          timer: null
        });
      }
    }
  }
});
