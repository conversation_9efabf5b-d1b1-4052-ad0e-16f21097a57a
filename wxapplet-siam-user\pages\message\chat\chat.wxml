<view class="chat-container">
  <!-- 顶部订单信息 -->
  <view class="order-info">
    <view class="order-title">订单号: {{orderNo}}</view>
  </view>

  <!-- 聊天记录区域 -->
  <scroll-view scroll-y="true" class="chat-messages" scroll-top="{{scrollTop}}" scroll-with-animation="true">
    <view class="message-list">
      <view wx:for="{{messageList}}" wx:key="index" class="message-item {{item.sender === 'me' ? 'message-mine' : 'message-other'}}">
        <!-- 对方消息 -->
        <block wx:if="{{item.sender === 'other'}}">
          <image src="{{item.avatar}}" class="avatar"></image>
          <view class="message-content-wrapper">
            <text class="message-time">{{item.time}}</text>
            <view class="message-content">
              <text class="message-text">{{item.content}}</text>
            </view>
          </view>
        </block>

        <!-- 我的消息 -->
        <block wx:else>
          <view class="message-content-wrapper">
            <text class="message-time me">{{item.time}}</text>
            <view class="message-content">
              <text class="message-text">{{item.content}}</text>
            </view>
          </view>
          <image src="{{item.avatar}}" class="avatar"></image>
        </block>
      </view>
    </view>
  </scroll-view>
  
  <!-- 输入区域 -->
  <view class="chat-input-area">
    <input class="chat-input" type="text" value="{{inputMessage}}" placeholder="请输入消息..." bindinput="onInputChange" bindconfirm="sendMessage" confirm-type="send" />
    <view class="send-button theme-bg" bindtap="sendMessage">发送</view>
  </view>
</view>