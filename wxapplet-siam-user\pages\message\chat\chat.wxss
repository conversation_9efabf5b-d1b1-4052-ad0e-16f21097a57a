page {
  height: 100%;
  background-color: #f5f5f5;
}

.chat-container {
  position: relative;
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f5f5;
}

/* 顶部订单信息样式 */
.order-info {
  padding: 20rpx 30rpx;
  background-color: #ffffff;
  border-bottom: 1rpx solid #e0e0e0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.order-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333333;
}

.order-status {
  font-size: 24rpx;
  color: #fa7c25;
}

/* 聊天记录区域样式 */
.chat-messages {
  flex: 1;
  padding: 20rpx 30rpx;
  box-sizing: border-box;
  padding-bottom: 120rpx;
  background-color: #f5f5f5;
  height: calc(100vh - 180rpx);
}

.message-list {
  display: flex;
  flex-direction: column;
}

.message-item {
  display: flex;
  margin-bottom: 30rpx;
}

.message-other {
  justify-content: flex-start;
}

.message-mine {
  justify-content: flex-end;
}

.avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  flex-shrink: 0;
  margin: 0 20rpx;
}

.message-content-wrapper {
  display: flex;
  flex-direction: column;
  max-width: 70%;
}

.message-time {
  font-size: 20rpx;
  color: #999999;
  margin-bottom: 10rpx;
}

.message-time.me {
  text-align: right;
}

.message-content {
  padding: 20rpx;
  border-radius: 10rpx;
  position: relative;
}

.message-other .message-content {
  background-color: #ffffff;
  border: 1rpx solid #e0e0e0;
}

.message-mine .message-content {
  background-color: #409EFF;
  color: #ffffff;
}

.message-text {
  font-size: 28rpx;
  word-wrap: break-word;
  word-break: break-all;
}

/* 输入区域样式 */
.chat-input-area {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  /* padding: 20rpx 0; */
  background-color: #ffffff;
  border-top: 1rpx solid #e0e0e0;
}

.chat-input {
  flex: 1 1 auto;
  padding: 15rpx 20rpx;
  border: 1rpx solid #e0e0e0;
  border-radius: 8rpx;
  font-size: 28rpx;
}

.send-button {
  /* margin-left: 20rpx; */
  color: #ffffff;
  border: none;
  /* border-radius: 8rpx; */
  font-size: 28rpx;
  line-height: 2.6;
  padding: 0 20rpx;
  width: 120rpx;
  flex-shrink: 0;
  display: inline-block;
  text-align: center;
}